{"name": "vito-interface", "version": "1.0.0", "description": "A secure and efficient application designed to interact with safe{wallet} through a minimalist Vito interface", "main": "index.js", "scripts": {"start:client": "cd client && npm start", "start:server": "cd server && npm run dev", "dev": "concurrently \"npm run start:server\" \"npm run start:client\"", "install:all": "npm install && cd client && npm install && cd ../server && npm install", "build": "cd client && npm run build && cd ../server && npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["safe-wallet", "vito-interface", "ethereum"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.0"}, "dependencies": {"@types/express": "^4.17.21", "express": "^4.18.2"}}