name: Pull Request Checks

on:
  pull_request:
    branches: [ main, develop ]
    types: [opened, synchronize, reopened, ready_for_review]

jobs:
  client-pr-tests:
    name: Client PR Tests
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false

    defaults:
      run:
        working-directory: ./client

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        cache-dependency-path: client/package-lock.json

    - name: Install dependencies
      run: npm ci

    - name: Run TypeScript type checking
      run: npx tsc --noEmit

    - name: Run Jest tests with coverage
      run: npm run test:ci
      env:
        CI: true

    - name: Build client
      run: npm run build
