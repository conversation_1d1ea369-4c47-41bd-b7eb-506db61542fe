name: Component Tests

on:
  push:
    paths:
      - 'client/src/components/**/*.tsx'
      - 'client/src/components/**/*.ts'
  pull_request:
    paths:
      - 'client/src/components/**/*.tsx'
      - 'client/src/components/**/*.ts'

jobs:
  address-book-selector-tests:
    name: AddressBookSelector Component Tests
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: ./client

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        cache-dependency-path: client/package-lock.json

    - name: Install dependencies
      run: npm ci

    - name: Run AddressBookSelector tests
      run: npm run test:component
      env:
        CI: true

    - name: Upload test coverage
      uses: actions/upload-artifact@v4
      with:
        name: addressbook-selector-coverage
        path: client/coverage/
        retention-days: 7
