name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  client-tests:
    name: Client Tests & Linting
    runs-on: ubuntu-latest
    
    defaults:
      run:
        working-directory: ./client

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: client/package-lock.json

    - name: Install dependencies
      run: npm ci

    - name: Run TypeScript type checking
      run: npx tsc --noEmit

    - name: Run Jest tests
      run: npm test -- --coverage --watchAll=false --passWithNoTests
      env:
        CI: true

    - name: Upload test coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./client/coverage/lcov.info
        flags: client
        name: client-coverage
        fail_ci_if_error: false

    - name: Build client
      run: npm run build
