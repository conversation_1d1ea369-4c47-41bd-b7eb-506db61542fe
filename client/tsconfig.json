{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@components/*": ["src/components/*"], "@utils": ["src/utils"], "@utils/*": ["src/utils/*"], "@hooks/*": ["src/hooks/*"], "@styles/*": ["src/styles/*"], "@pages/*": ["src/pages/*"], "@models/*": ["src/models/*"], "@vimUI/*": ["src/components/vimUI/*"]}}, "include": ["src"]}