import { ethers } from 'ethers';
import { ERC20_ABI, TOKEN_ADDRESSES } from '../contracts/abis';

export interface TokenInfo {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  balance?: string; // Optional balance for display
}

export interface TokenBalance {
  tokenInfo: TokenInfo;
  balance: string;
  formattedBalance: string;
}

export class TokenService {
  private provider: ethers.providers.Provider;
  private network: string;
  private tokenCache: Map<string, TokenInfo> = new Map();

  constructor(provider: ethers.providers.Provider, network: string) {
    this.provider = provider;
    this.network = network;
    this.initializeKnownTokens();
  }

  /**
   * Initialize cache with known tokens for the current network
   */
  private initializeKnownTokens(): void {
    const networkTokens = TOKEN_ADDRESSES[this.network as keyof typeof TOKEN_ADDRESSES];
    if (networkTokens) {
      // Pre-populate cache with known token addresses
      Object.entries(networkTokens).forEach(([symbol, address]) => {
        // We'll fetch full token info when needed
        this.tokenCache.set(address.toLowerCase(), {
          address: address.toLowerCase(),
          symbol,
          name: symbol, // Will be updated when full info is fetched
          decimals: 18 // Default, will be updated when full info is fetched
        });
      });
    }
  }

  /**
   * Get token information by address
   */
  async getTokenInfo(tokenAddress: string): Promise<TokenInfo | null> {
    const address = tokenAddress.toLowerCase();
    
    // Check cache first
    if (this.tokenCache.has(address)) {
      return this.tokenCache.get(address)!;
    }

    try {
      // Create contract instance
      const contract = new ethers.Contract(address, ERC20_ABI, this.provider);

      // Fetch token metadata
      const [symbol, name, decimals] = await Promise.all([
        contract.symbol(),
        contract.name(),
        contract.decimals()
      ]);

      const tokenInfo: TokenInfo = {
        address,
        symbol,
        name,
        decimals
      };

      // Cache the result
      this.tokenCache.set(address, tokenInfo);
      return tokenInfo;

    } catch (error) {
      console.error(`Error fetching token info for ${address}:`, error);
      return null;
    }
  }

  /**
   * Get native token info (ETH)
   */
  getNativeTokenInfo(): TokenInfo {
    const nativeTokens: { [key: string]: TokenInfo } = {
      ethereum: {
        address: '******************************************',
        symbol: 'ETH',
        name: 'Ethereum',
        decimals: 18
      },
      sepolia: {
        address: '******************************************',
        symbol: 'ETH',
        name: 'Ethereum',
        decimals: 18
      },
      arbitrum: {
        address: '******************************************',
        symbol: 'ETH',
        name: 'Ethereum',
        decimals: 18
      }
    };

    return nativeTokens[this.network] || nativeTokens.ethereum;
  }

  /**
   * Format token amount with proper decimals
   */
  formatTokenAmount(amount: string, decimals: number): string {
    try {
      const value = ethers.BigNumber.from(amount);
      if (value.isZero()) return '0';
      
      const formatted = ethers.utils.formatUnits(value, decimals);
      const num = parseFloat(formatted);
      
      if (num >= 1000) {
        return num.toLocaleString(undefined, { maximumFractionDigits: 2 });
      } else if (num >= 1) {
        return num.toFixed(4);
      } else {
        return num.toFixed(6);
      }
    } catch {
      return '0';
    }
  }

  /**
   * Check if an address is a token contract
   */
  async isTokenContract(address: string): Promise<boolean> {
    try {
      const contract = new ethers.Contract(address, ERC20_ABI, this.provider);
      // Try to call a standard ERC20 function
      await contract.symbol();
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get token balance for a specific address
   */
  async getTokenBalance(tokenAddress: string, walletAddress: string): Promise<string> {
    try {
      const contract = new ethers.Contract(tokenAddress, ERC20_ABI, this.provider);
      const balance = await contract.balanceOf(walletAddress);
      return balance.toString();
    } catch (error) {
      console.error(`Error fetching token balance for ${tokenAddress}:`, error);
      return '0';
    }
  }

  /**
   * Get formatted token balance with proper decimals
   */
  async getFormattedTokenBalance(tokenAddress: string, walletAddress: string): Promise<TokenBalance | null> {
    try {
      const tokenInfo = await this.getTokenInfo(tokenAddress);
      if (!tokenInfo) return null;

      const balance = await this.getTokenBalance(tokenAddress, walletAddress);
      const formattedBalance = this.formatTokenAmount(balance, tokenInfo.decimals);

      return {
        tokenInfo,
        balance,
        formattedBalance
      };
    } catch (error) {
      console.error(`Error getting formatted token balance:`, error);
      return null;
    }
  }

  /**
   * Get known tokens for the current network
   */
  getKnownTokens(): TokenInfo[] {
    const networkTokens = TOKEN_ADDRESSES[this.network as keyof typeof TOKEN_ADDRESSES];
    if (!networkTokens) return [];

    return Object.entries(networkTokens).map(([symbol, address]) => ({
      address: address.toLowerCase(),
      symbol,
      name: symbol,
      decimals: 18 // Default, will be updated when full info is fetched
    }));
  }

  /**
   * Get popular tokens with balances for a wallet
   */
  async getTokenBalances(walletAddress: string): Promise<TokenBalance[]> {
    const knownTokens = this.getKnownTokens();
    const balances: TokenBalance[] = [];

    for (const token of knownTokens) {
      try {
        const balance = await this.getFormattedTokenBalance(token.address, walletAddress);
        if (balance && parseFloat(balance.formattedBalance) > 0) {
          balances.push(balance);
        }
      } catch (error) {
        console.error(`Error fetching balance for ${token.symbol}:`, error);
      }
    }

    return balances;
  }

  /**
   * Get additional popular token addresses for the current network
   * This includes tokens that might not be in TOKEN_ADDRESSES but are commonly used
   */
  getPopularTokenAddresses(): string[] {
    const popularTokens: { [network: string]: string[] } = {
      ethereum: [
        '******************************************', // USDC
        '******************************************', // DAI
        '******************************************', // USDT
        '******************************************', // WBTC
        '******************************************', // WETH
        '******************************************', // UNI
        '******************************************', // AAVE
        '******************************************', // LINK
      ],
      sepolia: [
        '******************************************', // USDC (testnet)
        '******************************************', // LINK (testnet)
      ],
      polygon: [
        '******************************************', // USDC
        '******************************************', // DAI
        '******************************************', // USDT
        '******************************************', // WBTC
        '******************************************', // WETH
      ],
      arbitrum: [
        '******************************************', // USDC
        '******************************************', // DAI
        '******************************************', // USDT
        '******************************************', // WBTC
        '******************************************', // WETH
      ]
    };

    return popularTokens[this.network] || [];
  }

  /**
   * Get all tokens (known + popular) with balances for a wallet
   */
  async getAllTokenBalances(walletAddress: string): Promise<TokenBalance[]> {
    const knownTokens = this.getKnownTokens();
    const popularAddresses = this.getPopularTokenAddresses();

    // Combine known tokens with popular token addresses
    const allTokenAddresses = Array.from(new Set([
      ...knownTokens.map(t => t.address.toLowerCase()),
      ...popularAddresses.map(addr => addr.toLowerCase())
    ]));

    const balances: TokenBalance[] = [];

    for (const address of allTokenAddresses) {
      try {
        console.log(`🔍 Checking token balance for ${address}...`);
        const balance = await this.getFormattedTokenBalance(address, walletAddress);
        if (balance && parseFloat(balance.formattedBalance) > 0) {
          console.log(`✅ Found balance: ${balance.formattedBalance} ${balance.tokenInfo.symbol}`);
          balances.push(balance);
        } else {
          console.log(`⚪ Zero balance for token at ${address}`);
        }
      } catch (error) {
        console.warn(`❌ Error fetching balance for token ${address}:`, error);
      }
    }

    return balances;
  }

  /**
   * Validate if an address is a valid ERC-20 token
   */
  async validateTokenAddress(address: string): Promise<boolean> {
    try {
      if (!ethers.utils.isAddress(address)) {
        return false;
      }

      const tokenInfo = await this.getTokenInfo(address);
      return tokenInfo !== null;
    } catch {
      return false;
    }
  }

  /**
   * Clear token cache
   */
  clearCache(): void {
    this.tokenCache.clear();
    this.initializeKnownTokens();
  }
}
