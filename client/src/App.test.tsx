import React from 'react';
import { render, screen } from '@testing-library/react';

// Mock the WalletPage component to avoid module resolution issues
jest.mock('./components/wallet/WalletPage', () => {
  return function MockWalletPage() {
    return <div data-testid="wallet-page">Wallet Page</div>;
  };
});

// Mock other dependencies
jest.mock('./components/vitoUI', () => ({
  VitoContainer: ({ children }: any) => <div data-testid="vito-container">{children}</div>
}));

jest.mock('./utils', () => ({
  resolveAddressToEns: jest.fn(),
  isValidEthereumAddress: jest.fn()
}));

jest.mock('./components/ui', () => ({
  Button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  Input: ({ ...props }: any) => <input {...props} />,
  Card: ({ children, ...props }: any) => <div {...props}>{children}</div>
}));

import App from './App';

test('renders app component', () => {
  render(<App />);
  const walletPage = screen.getByTestId('wallet-page');
  expect(walletPage).toBeInTheDocument();
});
