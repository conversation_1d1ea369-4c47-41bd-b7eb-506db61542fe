/* Vito Interface - Dark Theme Styles */

/* Reset and base styles */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 500;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #0f172a;
  color: #ffffff;
}

#root {
  height: 100%;
  background: #0f172a;
}

/* Remove default React styles */
.App {
  height: 100%;
  background: transparent;
}

/* Code styling */
code {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  background-color: #1e293b; /* gray-800 */
  color: #e2e8f0; /* gray-200 */
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

/* Scrollbar styling for dark theme */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b; /* gray-800 */
}

::-webkit-scrollbar-thumb {
  background: #475569; /* gray-600 */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b; /* gray-500 */
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid #3b82f6; /* blue-500 */
  outline-offset: 2px;
}

/* Selection styling */
::selection {
  background-color: #3b82f6; /* blue-500 */
  color: white;
}

/* Smooth transitions */
* {
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

/* Logo animation */
@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.logo-spin {
  animation: logo-spin infinite 20s linear;
}

/* Flat accent text utilities */
.accent-text {
  color: #3b82f6; /* solid blue-500 for flat design */
}

.accent-text-secondary {
  color: #8b5cf6; /* solid purple-500 for flat design */
}

/* Glass effect utilities */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Dark theme overrides for any remaining default styles */
input, textarea, select, button {
  color: inherit;
  background-color: transparent;
}

/* DARK THEME WITH HIGH CONTRAST */
h1, h2, h3, h4, h5, h6 {
  color: #ffffff;
  font-weight: 700;
  line-height: 1.2;
  margin: 0;
  letter-spacing: -0.025em;
}

h1 { font-size: 2.5rem; font-weight: 800; }
h2 { font-size: 2rem; font-weight: 700; }
h3 { font-size: 1.75rem; font-weight: 700; }
h4 { font-size: 1.5rem; font-weight: 700; }
h5 { font-size: 1.25rem; font-weight: 700; }
h6 { font-size: 1.125rem; font-weight: 700; }

/* HIGH CONTRAST TEXT */
p, span, div, label {
  color: #ffffff;
  font-weight: 600;
  line-height: 1.6;
}

/* High contrast utilities */
.text-primary {
  color: #60a5fa;
  font-weight: 700;
}

.text-secondary {
  color: #e5e7eb;
  font-weight: 600;
}

.text-muted {
  color: #d1d5db;
  font-weight: 600;
}

.text-success {
  color: #34d399;
  font-weight: 700;
}

.text-warning {
  color: #fbbf24;
  font-weight: 700;
}

.text-danger {
  color: #f87171;
  font-weight: 700;
}

/* Dark card */
.dark-card {
  background: #1e293b;
  border: 1px solid #334155;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

body {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
}
