# Vito Safe Wallet Interface - Environment Variables Template
# Copy this file to .env.local and fill in your actual values

# API Keys for Ethereum providers
REACT_APP_INFURA_KEY=YOUR_INFURA_KEY_HERE
REACT_APP_ALCHEMY_KEY=YOUR_ALCHEMY_KEY_HERE

# Etherscan API Key (get free key from etherscan.io)
REACT_APP_ETHERSCAN_API_KEY=YOUR_ETHERSCAN_API_KEY_HERE

# Safe TX Pool Contract Addresses
# These should be the deployed SafeTxPool contract addresses for each network
# Replace with actual deployed contract addresses

# Ethereum Mainnet SafeTxPool Contract
REACT_APP_SAFE_TX_POOL_ETHEREUM=******************************************

# Sepolia Testnet SafeTxPool Contract
REACT_APP_SAFE_TX_POOL_SEPOLIA=******************************************

# Arbitrum One SafeTxPool Contract
REACT_APP_SAFE_TX_POOL_ARBITRUM=******************************************

# Optional: Custom RPC URLs (if not using Alchemy/Infura)
# REACT_APP_ETHEREUM_RPC_URL=https://your-custom-ethereum-rpc
# REACT_APP_SEPOLIA_RPC_URL=https://your-custom-sepolia-rpc
# REACT_APP_ARBITRUM_RPC_URL=https://your-custom-arbitrum-rpc

# Development Settings
REACT_APP_ENVIRONMENT=development

# Enable/Disable Features
REACT_APP_ENABLE_SAFE_TX_POOL=true
REACT_APP_ENABLE_NETWORK_SWITCHING=true