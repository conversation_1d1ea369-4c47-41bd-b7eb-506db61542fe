{"name": "server", "version": "1.0.0", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "MIT", "description": "", "dependencies": {"@types/express": "^4.17.21", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.18.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/node": "^22.14.0", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "typescript": "^5.8.2"}}